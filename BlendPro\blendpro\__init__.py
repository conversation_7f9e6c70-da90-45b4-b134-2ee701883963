"""
BlendPro Package
===============

Modern AI-powered Blender assistant package.
"""

import sys
from pathlib import Path

# Ensure lib directory is in Python path for dependencies
current_dir = Path(__file__).parent.parent
lib_path = current_dir / "lib"
if str(lib_path) not in sys.path:
    sys.path.insert(0, str(lib_path))

__version__ = "1.0.0"
__author__ = "inkbytefo"
__license__ = "MIT"

# Package metadata
PACKAGE_NAME = "BlendPro"
PACKAGE_DESCRIPTION = "Modern AI-powered Blender assistant with multi-model support"
PACKAGE_URL = "https://github.com/inkbytefo/BlendPro"

# Supported AI models
SUPPORTED_MODELS = {
    "openai": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo"],
    "anthropic": ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307"],
    "google": ["gemini-1.5-pro", "gemini-1.5-flash"],
}

# Default configuration
DEFAULT_CONFIG = {
    "ai_provider": "openai",
    "model": "gpt-4o-mini",
    "max_tokens": 2000,
    "temperature": 0.1,
    "timeout": 30,
    "auto_execute": False,
    "save_history": True,
    "max_history": 50,
}


def get_lib_path():
    """Get the lib directory path for dependencies"""
    current_dir = Path(__file__).parent.parent
    return current_dir / "lib"


def is_lib_in_path():
    """Check if lib directory is in Python path"""
    lib_path = get_lib_path()
    return str(lib_path) in sys.path


def ensure_lib_path():
    """Ensure lib directory is in Python path"""
    lib_path = get_lib_path()
    if str(lib_path) not in sys.path:
        sys.path.insert(0, str(lib_path))
        return True
    return False


def get_dependency_status():
    """Get status of key dependencies"""
    dependencies = {
        "openai": False,
        "anthropic": False,
        "google.generativeai": False,
        "requests": False,
        "yaml": False,
        "aiohttp": False,
    }

    for dep in dependencies:
        try:
            __import__(dep)
            dependencies[dep] = True
        except ImportError:
            dependencies[dep] = False

    return dependencies


def print_debug_info():
    """Print debug information about lib path and dependencies"""
    print(f"BlendPro Debug Info:")
    print(f"  Lib path: {get_lib_path()}")
    print(f"  Lib exists: {get_lib_path().exists()}")
    print(f"  Lib in sys.path: {is_lib_in_path()}")

    deps = get_dependency_status()
    print(f"  Dependencies:")
    for dep, status in deps.items():
        status_str = "✓" if status else "✗"
        print(f"    {status_str} {dep}")

    return deps
