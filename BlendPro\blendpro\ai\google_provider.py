"""
Google Provider
==============

Google Generative AI (Gemini) integration for BlendPro.
Supports Gemini-1.5-Pro and Gemini-1.5-Flash models.
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Optional, AsyncGenerator, Dict, Any
import re

# Ensure lib directory is in Python path for Google dependencies
current_dir = Path(__file__).parent.parent.parent
lib_path = current_dir / "lib"
if str(lib_path) not in sys.path:
    sys.path.insert(0, str(lib_path))

try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    genai = None

from .base import (
    AIProvider, AIMessage, AIResponse, AIConfig, AIError, 
    AIProviderError, AIRateLimitError, AITimeoutError,
    BLENDER_SYSTEM_PROMPT
)
from ..utils.logger import get_logger


class GoogleProvider(AIProvider):
    """Google Generative AI (Gemini) provider"""
    
    def __init__(self, api_key: str, config: Optional[AIConfig] = None):
        if not GOOGLE_AVAILABLE:
            raise AIProviderError("Google Generative AI library not available. Install with: pip install google-generativeai>=0.8.0")
        
        super().__init__(api_key, config)
        self.logger = get_logger(f"{__name__}.GoogleProvider")
        self._client = None
        
        # Configure the API
        genai.configure(api_key=self.api_key)
        
    @property
    def name(self) -> str:
        return "google"
    
    @property
    def supported_models(self) -> List[str]:
        return [
            "gemini-1.5-pro",
            "gemini-1.5-flash"
        ]
    
    def _get_client(self, model: str):
        """Get or create Google Generative AI client"""
        # Configure safety settings to be less restrictive for code generation
        safety_settings = {
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
        }
        
        return genai.GenerativeModel(
            model_name=model,
            safety_settings=safety_settings,
            system_instruction=BLENDER_SYSTEM_PROMPT
        )
    
    def validate_api_key(self) -> bool:
        """Validate the Google API key"""
        try:
            # Try to list models to validate API key
            models = list(genai.list_models())
            return len(models) > 0
        except Exception as e:
            self.logger.error(f"Google API key validation failed: {e}")
            return False
    
    def _convert_messages(self, messages: List[AIMessage]) -> List[Dict[str, str]]:
        """Convert AIMessage objects to Google format"""
        google_messages = []
        
        for msg in messages:
            if msg.role == "system":
                # System messages are handled via system_instruction in model creation
                continue
            elif msg.role == "assistant":
                role = "model"
            else:
                role = "user"
                
            google_messages.append({
                "role": role,
                "parts": [msg.content]
            })
        
        return google_messages
    
    def _extract_code(self, content: str) -> str:
        """Extract Python code from response"""
        # Remove markdown code blocks
        code_pattern = r'```(?:python)?\s*(.*?)\s*```'
        matches = re.findall(code_pattern, content, re.DOTALL)
        
        if matches:
            return matches[0].strip()
        
        # If no code blocks found, return the content as-is
        return content.strip()
    
    async def generate_response(
        self, 
        messages: List[AIMessage], 
        config: Optional[AIConfig] = None
    ) -> AIResponse:
        """Generate a response using Google Generative AI"""
        
        if not messages:
            raise AIProviderError("No messages provided")
        
        # Use provided config or default
        cfg = config or self.config
        model = self._get_client(cfg.model)
        
        try:
            self.logger.debug(f"Generating response with model: {cfg.model}")
            
            # Convert messages to Google format
            google_messages = self._convert_messages(messages)
            
            # Get the last user message for generation
            last_message = None
            for msg in reversed(messages):
                if msg.role == "user":
                    last_message = msg.content
                    break
            
            if not last_message:
                raise AIProviderError("No user message found")
            
            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=cfg.max_tokens,
                temperature=cfg.temperature,
            )
            
            # Generate response
            response = await asyncio.wait_for(
                asyncio.to_thread(
                    model.generate_content,
                    last_message,
                    generation_config=generation_config
                ),
                timeout=cfg.timeout
            )
            
            # Extract response content
            content = response.text if response.text else ""
            if not content:
                raise AIProviderError("Empty response from Google")
            
            # Extract code from response
            code_content = self._extract_code(content)
            
            # Create response object
            usage_metadata = response.usage_metadata if hasattr(response, 'usage_metadata') else None
            ai_response = AIResponse(
                content=code_content,
                model=cfg.model,
                provider=self.name,
                usage={
                    "prompt_tokens": usage_metadata.prompt_token_count if usage_metadata else 0,
                    "completion_tokens": usage_metadata.candidates_token_count if usage_metadata else 0,
                    "total_tokens": usage_metadata.total_token_count if usage_metadata else 0,
                },
                finish_reason=response.candidates[0].finish_reason.name if response.candidates else None
            )
            
            self.logger.debug(f"Response generated successfully. Tokens used: {ai_response.usage.get('total_tokens', 0)}")
            return ai_response
            
        except asyncio.TimeoutError:
            raise AITimeoutError(f"Google request timed out after {cfg.timeout} seconds")
        except Exception as e:
            error_msg = str(e).lower()
            if "quota" in error_msg or "rate limit" in error_msg:
                raise AIRateLimitError(f"Google rate limit exceeded: {e}")
            elif "invalid api key" in error_msg or "unauthorized" in error_msg:
                raise AIProviderError(f"Invalid Google API key: {e}")
            else:
                raise AIProviderError(f"Google API error: {e}")
    
    async def generate_stream(
        self, 
        messages: List[AIMessage], 
        config: Optional[AIConfig] = None
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response using Google Generative AI"""
        
        if not messages:
            raise AIProviderError("No messages provided")
        
        # Use provided config or default
        cfg = config or self.config
        model = self._get_client(cfg.model)
        
        try:
            self.logger.debug(f"Generating streaming response with model: {cfg.model}")
            
            # Get the last user message for generation
            last_message = None
            for msg in reversed(messages):
                if msg.role == "user":
                    last_message = msg.content
                    break
            
            if not last_message:
                raise AIProviderError("No user message found")
            
            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=cfg.max_tokens,
                temperature=cfg.temperature,
            )
            
            # Generate streaming response
            response_stream = await asyncio.wait_for(
                asyncio.to_thread(
                    model.generate_content,
                    last_message,
                    generation_config=generation_config,
                    stream=True
                ),
                timeout=cfg.timeout
            )
            
            # Yield chunks as they arrive
            for chunk in response_stream:
                if chunk.text:
                    yield chunk.text
                    
        except asyncio.TimeoutError:
            raise AITimeoutError(f"Google streaming request timed out after {cfg.timeout} seconds")
        except Exception as e:
            error_msg = str(e).lower()
            if "quota" in error_msg or "rate limit" in error_msg:
                raise AIRateLimitError(f"Google rate limit exceeded: {e}")
            elif "invalid api key" in error_msg or "unauthorized" in error_msg:
                raise AIProviderError(f"Invalid Google API key: {e}")
            else:
                raise AIProviderError(f"Google streaming error: {e}")
    
    def get_default_config(self) -> AIConfig:
        """Get default configuration for Google"""
        return AIConfig(
            model="gemini-1.5-flash",
            max_tokens=2000,
            temperature=0.1,
            timeout=30,
            stream=False,
            system_prompt=BLENDER_SYSTEM_PROMPT
        )
