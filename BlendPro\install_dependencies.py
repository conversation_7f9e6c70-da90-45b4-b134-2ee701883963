#!/usr/bin/env python3
"""
BlendPro Dependency Installer
============================

Automated script to install all required dependencies for BlendPro
into the local lib directory.

Author: inkbytefo
"""

import sys
import os
import subprocess
from pathlib import Path
import platform

def get_python_executable():
    """Get the appropriate Python executable"""
    # Try to use the same Python that's running this script
    return sys.executable

def install_dependencies():
    """Install dependencies to lib directory"""
    print("BlendPro Dependency Installer")
    print("=" * 50)
    
    # Get paths
    script_dir = Path(__file__).parent
    lib_dir = script_dir / "lib"
    requirements_file = script_dir / "requirements.txt"
    
    print(f"Script directory: {script_dir}")
    print(f"Lib directory: {lib_dir}")
    print(f"Requirements file: {requirements_file}")
    
    # Check if requirements.txt exists
    if not requirements_file.exists():
        print(f"❌ Requirements file not found: {requirements_file}")
        return False
    
    # Create lib directory if it doesn't exist
    lib_dir.mkdir(exist_ok=True)
    print(f"✓ Lib directory ready: {lib_dir}")
    
    # Get Python executable
    python_exe = get_python_executable()
    print(f"✓ Using Python: {python_exe}")
    
    # Platform info
    print(f"✓ Platform: {platform.system()} {platform.machine()}")
    print(f"✓ Python version: {sys.version}")
    
    # Install command
    cmd = [
        python_exe, "-m", "pip", "install",
        "-r", str(requirements_file),
        "--target", str(lib_dir),
        "--upgrade",
        "--no-deps"  # Install without dependencies to avoid conflicts
    ]
    
    print(f"\nInstalling dependencies...")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        # Run pip install
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(result.stdout)
        if result.stderr:
            print("Warnings/Info:")
            print(result.stderr)
        
        print("-" * 50)
        print("✅ Dependencies installed successfully!")
        
        # Now install with dependencies
        cmd_with_deps = [
            python_exe, "-m", "pip", "install",
            "-r", str(requirements_file),
            "--target", str(lib_dir),
            "--upgrade"
        ]
        
        print(f"\nInstalling with dependencies...")
        print(f"Command: {' '.join(cmd_with_deps)}")
        print("-" * 50)
        
        result = subprocess.run(cmd_with_deps, capture_output=True, text=True, check=True)
        print(result.stdout)
        if result.stderr:
            print("Warnings/Info:")
            print(result.stderr)
        
        print("-" * 50)
        print("✅ All dependencies installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed with return code {e.returncode}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_installation():
    """Test if dependencies can be imported"""
    print(f"\nTesting installation...")
    print("-" * 30)
    
    # Add lib to path
    script_dir = Path(__file__).parent
    lib_dir = script_dir / "lib"
    
    if str(lib_dir) not in sys.path:
        sys.path.insert(0, str(lib_dir))
    
    # Test key dependencies
    dependencies = [
        ("openai", "OpenAI"),
        ("anthropic", "Anthropic"),
        ("google.generativeai", "Google Generative AI"),
        ("requests", "Requests"),
        ("yaml", "PyYAML"),
        ("aiohttp", "aiohttp"),
    ]
    
    success_count = 0
    total_count = len(dependencies)
    
    for module_name, display_name in dependencies:
        try:
            __import__(module_name)
            print(f"✓ {display_name}: OK")
            success_count += 1
        except ImportError as e:
            print(f"✗ {display_name}: FAILED - {e}")
    
    print("-" * 30)
    print(f"Test Results: {success_count}/{total_count} dependencies working")
    
    if success_count == total_count:
        print("🎉 All dependencies are working correctly!")
        return True
    else:
        print("⚠️  Some dependencies failed to import.")
        return False

def main():
    """Main installation function"""
    try:
        # Install dependencies
        install_success = install_dependencies()
        
        if install_success:
            # Test installation
            test_success = test_installation()
            
            if test_success:
                print(f"\n🎉 BlendPro is ready to use!")
                print(f"You can now enable the addon in Blender.")
                return True
            else:
                print(f"\n⚠️  Installation completed but some tests failed.")
                print(f"BlendPro may still work, but some features might be limited.")
                return False
        else:
            print(f"\n❌ Installation failed.")
            print(f"Please check the error messages above and try again.")
            return False
            
    except KeyboardInterrupt:
        print(f"\n⚠️  Installation cancelled by user.")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input(f"\nPress Enter to exit...")
    sys.exit(0 if success else 1)
