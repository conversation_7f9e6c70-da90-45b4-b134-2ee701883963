#!/usr/bin/env python3
"""
BlendPro Lib Import Test
=======================

Test script to verify that the lib directory is properly configured
and dependencies can be imported successfully.

Author: inkbytefo
"""

import sys
import os
from pathlib import Path

def test_lib_path():
    """Test if lib directory is in Python path"""
    print("=== BlendPro Lib Path Test ===")
    
    # Get the addon directory
    addon_dir = Path(__file__).parent
    lib_path = addon_dir / "lib"
    
    print(f"Addon directory: {addon_dir}")
    print(f"Lib directory: {lib_path}")
    print(f"Lib exists: {lib_path.exists()}")
    
    if lib_path.exists():
        print(f"Lib contents: {list(lib_path.iterdir())[:10]}...")  # Show first 10 items
    
    print(f"Lib in sys.path: {str(lib_path) in sys.path}")
    
    # Add lib to path if not already there
    if str(lib_path) not in sys.path:
        sys.path.insert(0, str(lib_path))
        print("Added lib to sys.path")
    
    return lib_path

def test_dependencies():
    """Test importing key dependencies"""
    print("\n=== Dependency Import Test ===")
    
    dependencies = [
        ("openai", "OpenAI"),
        ("anthropic", "Anthropic"),
        ("google.generativeai", "Google Generative AI"),
        ("requests", "Requests"),
        ("yaml", "PyYAML"),
        ("aiohttp", "aiohttp"),
    ]
    
    results = {}
    
    for module_name, display_name in dependencies:
        try:
            __import__(module_name)
            print(f"✓ {display_name}: OK")
            results[module_name] = True
        except ImportError as e:
            print(f"✗ {display_name}: FAILED - {e}")
            results[module_name] = False
    
    return results

def test_blendpro_imports():
    """Test importing BlendPro modules"""
    print("\n=== BlendPro Module Import Test ===")
    
    # Add blendpro to path
    addon_dir = Path(__file__).parent
    blendpro_path = addon_dir / "blendpro"
    if str(blendpro_path) not in sys.path:
        sys.path.insert(0, str(blendpro_path))
    
    modules = [
        ("blendpro", "BlendPro Package"),
        ("blendpro.ai", "AI Package"),
        ("blendpro.ai.openai_provider", "OpenAI Provider"),
        ("blendpro.ai.anthropic_provider", "Anthropic Provider"),
        ("blendpro.ai.google_provider", "Google Provider"),
        ("blendpro.utils.logger", "Logger Utils"),
    ]
    
    results = {}
    
    for module_name, display_name in modules:
        try:
            __import__(module_name)
            print(f"✓ {display_name}: OK")
            results[module_name] = True
        except ImportError as e:
            print(f"✗ {display_name}: FAILED - {e}")
            results[module_name] = False
    
    return results

def main():
    """Main test function"""
    print("BlendPro Dependency Test")
    print("=" * 50)
    
    # Test lib path
    lib_path = test_lib_path()
    
    # Test dependencies
    dep_results = test_dependencies()
    
    # Test BlendPro imports
    bp_results = test_blendpro_imports()
    
    # Summary
    print("\n=== Test Summary ===")
    total_deps = len(dep_results)
    successful_deps = sum(dep_results.values())
    
    total_bp = len(bp_results)
    successful_bp = sum(bp_results.values())
    
    print(f"Dependencies: {successful_deps}/{total_deps} successful")
    print(f"BlendPro modules: {successful_bp}/{total_bp} successful")
    
    if successful_deps == total_deps and successful_bp == total_bp:
        print("🎉 All tests passed! BlendPro is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
