"""
Anthropic Provider
=================

Anthropic Claude API integration for BlendPro.
Supports Claude-3.5-Sonnet and Claude-3-Haiku models.
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Optional, AsyncGenerator, Dict, Any
import re

# Ensure lib directory is in Python path for Anthropic dependencies
current_dir = Path(__file__).parent.parent.parent
lib_path = current_dir / "lib"
if str(lib_path) not in sys.path:
    sys.path.insert(0, str(lib_path))

try:
    from anthropic import AsyncAnthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    AsyncAnthropic = None

from .base import (
    AIProvider, AIMessage, AIResponse, AIConfig, AIError, 
    AIProviderError, AIRateLimitError, AITimeoutError,
    BLENDER_SYSTEM_PROMPT
)
from ..utils.logger import get_logger


class AnthropicProvider(AIProvider):
    """Anthropic Claude API provider"""
    
    def __init__(self, api_key: str, config: Optional[AIConfig] = None):
        if not ANTHROPIC_AVAILABLE:
            raise AIProviderError("Anthropic library not available. Install with: pip install anthropic>=0.40.0")
        
        super().__init__(api_key, config)
        self.logger = get_logger(f"{__name__}.AnthropicProvider")
        self._client = None
        
    @property
    def name(self) -> str:
        return "anthropic"
    
    @property
    def supported_models(self) -> List[str]:
        return [
            "claude-3-5-sonnet-20241022",
            "claude-3-haiku-20240307"
        ]
    
    def _get_client(self) -> AsyncAnthropic:
        """Get or create Anthropic client"""
        if self._client is None:
            self._client = AsyncAnthropic(api_key=self.api_key)
        return self._client
    
    def validate_api_key(self) -> bool:
        """Validate the Anthropic API key"""
        try:
            # Simple validation - try to create client
            client = AsyncAnthropic(api_key=self.api_key)
            return True
        except Exception as e:
            self.logger.error(f"Anthropic API key validation failed: {e}")
            return False
    
    def _convert_messages(self, messages: List[AIMessage]) -> tuple[str, List[Dict[str, str]]]:
        """Convert AIMessage objects to Anthropic format"""
        system_prompt = BLENDER_SYSTEM_PROMPT
        anthropic_messages = []
        
        for msg in messages:
            if msg.role == "system":
                system_prompt = msg.content
            else:
                anthropic_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        return system_prompt, anthropic_messages
    
    def _extract_code(self, content: str) -> str:
        """Extract Python code from response"""
        # Remove markdown code blocks
        code_pattern = r'```(?:python)?\s*(.*?)\s*```'
        matches = re.findall(code_pattern, content, re.DOTALL)
        
        if matches:
            return matches[0].strip()
        
        # If no code blocks found, return the content as-is
        return content.strip()
    
    async def generate_response(
        self, 
        messages: List[AIMessage], 
        config: Optional[AIConfig] = None
    ) -> AIResponse:
        """Generate a response using Anthropic Claude API"""
        
        if not messages:
            raise AIProviderError("No messages provided")
        
        # Use provided config or default
        cfg = config or self.config
        client = self._get_client()
        
        try:
            self.logger.debug(f"Generating response with model: {cfg.model}")
            
            # Convert messages to Anthropic format
            system_prompt, anthropic_messages = self._convert_messages(messages)
            
            # Make API call
            response = await client.messages.create(
                model=cfg.model,
                system=system_prompt,
                messages=anthropic_messages,
                max_tokens=cfg.max_tokens,
                temperature=cfg.temperature,
                timeout=cfg.timeout
            )
            
            # Extract response content
            content = response.content[0].text if response.content else ""
            if not content:
                raise AIProviderError("Empty response from Anthropic")
            
            # Extract code from response
            code_content = self._extract_code(content)
            
            # Create response object
            ai_response = AIResponse(
                content=code_content,
                model=cfg.model,
                provider=self.name,
                usage={
                    "input_tokens": response.usage.input_tokens if response.usage else 0,
                    "output_tokens": response.usage.output_tokens if response.usage else 0,
                    "total_tokens": (response.usage.input_tokens + response.usage.output_tokens) if response.usage else 0,
                },
                finish_reason=response.stop_reason
            )
            
            self.logger.debug(f"Response generated successfully. Tokens used: {ai_response.usage.get('total_tokens', 0)}")
            return ai_response
            
        except asyncio.TimeoutError:
            raise AITimeoutError(f"Anthropic request timed out after {cfg.timeout} seconds")
        except Exception as e:
            error_msg = str(e).lower()
            if "rate limit" in error_msg:
                raise AIRateLimitError(f"Anthropic rate limit exceeded: {e}")
            elif "invalid api key" in error_msg or "unauthorized" in error_msg:
                raise AIProviderError(f"Invalid Anthropic API key: {e}")
            else:
                raise AIProviderError(f"Anthropic API error: {e}")
    
    async def generate_stream(
        self, 
        messages: List[AIMessage], 
        config: Optional[AIConfig] = None
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response using Anthropic Claude API"""
        
        if not messages:
            raise AIProviderError("No messages provided")
        
        # Use provided config or default
        cfg = config or self.config
        client = self._get_client()
        
        try:
            self.logger.debug(f"Generating streaming response with model: {cfg.model}")
            
            # Convert messages to Anthropic format
            system_prompt, anthropic_messages = self._convert_messages(messages)
            
            # Make streaming API call
            async with client.messages.stream(
                model=cfg.model,
                system=system_prompt,
                messages=anthropic_messages,
                max_tokens=cfg.max_tokens,
                temperature=cfg.temperature,
                timeout=cfg.timeout
            ) as stream:
                async for text in stream.text_stream:
                    yield text
                    
        except asyncio.TimeoutError:
            raise AITimeoutError(f"Anthropic streaming request timed out after {cfg.timeout} seconds")
        except Exception as e:
            error_msg = str(e).lower()
            if "rate limit" in error_msg:
                raise AIRateLimitError(f"Anthropic rate limit exceeded: {e}")
            elif "invalid api key" in error_msg or "unauthorized" in error_msg:
                raise AIProviderError(f"Invalid Anthropic API key: {e}")
            else:
                raise AIProviderError(f"Anthropic streaming error: {e}")
    
    def get_default_config(self) -> AIConfig:
        """Get default configuration for Anthropic"""
        return AIConfig(
            model="claude-3-5-sonnet-20241022",
            max_tokens=2000,
            temperature=0.1,
            timeout=30,
            stream=False,
            system_prompt=BLENDER_SYSTEM_PROMPT
        )
