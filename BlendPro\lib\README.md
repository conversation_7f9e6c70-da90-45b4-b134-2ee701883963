# BlendPro Dependencies Library

This directory contains all the required Python dependencies for BlendPro to function properly within Blender. The dependencies are installed here to avoid conflicts with Blender's built-in Python environment and to ensure consistent behavior across different Blender installations.

## Purpose

The `lib` directory serves as a local Python package repository that contains:

- **OpenAI SDK** - For GPT-4o, GPT-4o-mini, and other OpenAI models
- **Anthropic SDK** - For Claude-3.5-Sonnet and Claude-3-Haiku models  
- **Google Generative AI** - For Gemini-1.5-Pro and Gemini-1.5-Flash models
- **HTTP Libraries** - requests, httpx, aiohttp for API communication
- **Validation Libraries** - pydantic, marshmallow for data validation
- **Utility Libraries** - PyYAML, python-dotenv, tenacity, etc.

## How It Works

BlendPro automatically adds this `lib` directory to Python's `sys.path` during initialization, ensuring that all required dependencies are available when the addon loads. This happens in multiple places:

1. **Main addon init** (`__init__.py`) - Adds lib path before importing any modules
2. **Package init** (`blendpro/__init__.py`) - Ensures lib path for package-level imports
3. **Module inits** - Each submodule ensures lib path availability
4. **Provider modules** - AI providers add lib path before importing their respective SDKs

## Installation

Dependencies should be installed using pip with the `--target` flag pointing to this directory:

```bash
# Install all dependencies from requirements.txt
pip install -r requirements.txt --target lib --upgrade

# Install specific packages
pip install openai --target lib --upgrade
pip install anthropic --target lib --upgrade
pip install google-generativeai --target lib --upgrade
```

## Platform Compatibility

The dependencies include platform-specific binary wheels for:
- Windows (x64, ARM64)
- macOS (x64, ARM64) 
- Linux (x64)

Binary files (`.pyd`, `.so`, `.dylib`) are included for optimal performance.

## Troubleshooting

If you encounter import errors:

1. **Check lib path**: Verify that the lib directory is in `sys.path`
2. **Reinstall dependencies**: Use pip with `--target lib --upgrade --force-reinstall`
3. **Platform mismatch**: Ensure dependencies match your platform architecture
4. **Binary compatibility**: Some binary dependencies may require specific Python versions

## Testing

Use the provided test script to verify dependencies:

```bash
python test_lib_import.py
```

This will check:
- Lib directory path configuration
- Dependency import status
- BlendPro module availability

## Maintenance

Keep dependencies updated by running:

```bash
pip install -r requirements.txt --target lib --upgrade
```

Check for security updates regularly and update the `requirements.txt` file as needed.

---

**Author**: inkbytefo  
**License**: MIT  
**BlendPro Version**: 1.0.0
