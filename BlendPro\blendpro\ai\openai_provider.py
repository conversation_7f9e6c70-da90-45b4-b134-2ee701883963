"""
OpenAI Provider
==============

Modern OpenAI API v1+ integration for BlendPro.
Supports GPT-4o, GPT-4o-mini, GPT-4-turbo, and GPT-3.5-turbo models.
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Optional, AsyncGenerator, Dict, Any
import re

# Ensure lib directory is in Python path for OpenAI dependencies
current_dir = Path(__file__).parent.parent.parent
lib_path = current_dir / "lib"
if str(lib_path) not in sys.path:
    sys.path.insert(0, str(lib_path))

try:
    from openai import AsyncOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    AsyncOpenAI = None

from .base import (
    AIProvider, AIMessage, AIResponse, AIConfig, AIError, 
    AIProviderError, AIRateLimitError, AITimeoutError,
    BLENDER_SYSTEM_PROMPT
)
from ..utils.logger import get_logger


class OpenAIProvider(AIProvider):
    """OpenAI API provider using the modern v1+ SDK"""
    
    def __init__(self, api_key: str, config: Optional[AIConfig] = None):
        if not OPENAI_AVAILABLE:
            raise AIProviderError("OpenAI library not available. Install with: pip install openai>=1.50.0")
        
        super().__init__(api_key, config)
        self.logger = get_logger(f"{__name__}.OpenAIProvider")
        self._client = None
        
    @property
    def name(self) -> str:
        return "openai"
    
    @property
    def supported_models(self) -> List[str]:
        return [
            "gpt-4o",
            "gpt-4o-mini", 
            "gpt-4-turbo",
            "gpt-3.5-turbo"
        ]
    
    def _get_client(self) -> AsyncOpenAI:
        """Get or create OpenAI client"""
        if self._client is None:
            self._client = AsyncOpenAI(api_key=self.api_key)
        return self._client
    
    def validate_api_key(self) -> bool:
        """Validate the OpenAI API key"""
        try:
            # Simple validation - try to create client
            client = AsyncOpenAI(api_key=self.api_key)
            return True
        except Exception as e:
            self.logger.error(f"OpenAI API key validation failed: {e}")
            return False
    
    def _convert_messages(self, messages: List[AIMessage]) -> List[Dict[str, str]]:
        """Convert AIMessage objects to OpenAI format"""
        openai_messages = []
        
        # Add system prompt if not present
        has_system = any(msg.role == "system" for msg in messages)
        if not has_system:
            openai_messages.append({
                "role": "system",
                "content": BLENDER_SYSTEM_PROMPT
            })
        
        # Convert messages
        for msg in messages:
            openai_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        return openai_messages
    
    def _extract_code(self, content: str) -> str:
        """Extract Python code from response"""
        # Remove markdown code blocks
        code_pattern = r'```(?:python)?\s*(.*?)\s*```'
        matches = re.findall(code_pattern, content, re.DOTALL)
        
        if matches:
            return matches[0].strip()
        
        # If no code blocks found, return the content as-is
        return content.strip()
    
    async def generate_response(
        self, 
        messages: List[AIMessage], 
        config: Optional[AIConfig] = None
    ) -> AIResponse:
        """Generate a response using OpenAI API"""
        
        if not messages:
            raise AIProviderError("No messages provided")
        
        # Use provided config or default
        cfg = config or self.config
        client = self._get_client()
        
        try:
            self.logger.debug(f"Generating response with model: {cfg.model}")
            
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Make API call
            response = await client.chat.completions.create(
                model=cfg.model,
                messages=openai_messages,
                max_tokens=cfg.max_tokens,
                temperature=cfg.temperature,
                timeout=cfg.timeout,
                stream=False
            )
            
            # Extract response content
            content = response.choices[0].message.content
            if not content:
                raise AIProviderError("Empty response from OpenAI")
            
            # Extract code from response
            code_content = self._extract_code(content)
            
            # Create response object
            ai_response = AIResponse(
                content=code_content,
                model=cfg.model,
                provider=self.name,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                    "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                    "total_tokens": response.usage.total_tokens if response.usage else 0,
                },
                finish_reason=response.choices[0].finish_reason
            )
            
            self.logger.debug(f"Response generated successfully. Tokens used: {ai_response.usage.get('total_tokens', 0)}")
            return ai_response
            
        except asyncio.TimeoutError:
            raise AITimeoutError(f"OpenAI request timed out after {cfg.timeout} seconds")
        except Exception as e:
            error_msg = str(e).lower()
            if "rate limit" in error_msg:
                raise AIRateLimitError(f"OpenAI rate limit exceeded: {e}")
            elif "invalid api key" in error_msg or "unauthorized" in error_msg:
                raise AIProviderError(f"Invalid OpenAI API key: {e}")
            else:
                raise AIProviderError(f"OpenAI API error: {e}")
    
    async def generate_stream(
        self, 
        messages: List[AIMessage], 
        config: Optional[AIConfig] = None
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response using OpenAI API"""
        
        if not messages:
            raise AIProviderError("No messages provided")
        
        # Use provided config or default
        cfg = config or self.config
        client = self._get_client()
        
        try:
            self.logger.debug(f"Generating streaming response with model: {cfg.model}")
            
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Make streaming API call
            stream = await client.chat.completions.create(
                model=cfg.model,
                messages=openai_messages,
                max_tokens=cfg.max_tokens,
                temperature=cfg.temperature,
                timeout=cfg.timeout,
                stream=True
            )
            
            # Yield chunks as they arrive
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except asyncio.TimeoutError:
            raise AITimeoutError(f"OpenAI streaming request timed out after {cfg.timeout} seconds")
        except Exception as e:
            error_msg = str(e).lower()
            if "rate limit" in error_msg:
                raise AIRateLimitError(f"OpenAI rate limit exceeded: {e}")
            elif "invalid api key" in error_msg or "unauthorized" in error_msg:
                raise AIProviderError(f"Invalid OpenAI API key: {e}")
            else:
                raise AIProviderError(f"OpenAI streaming error: {e}")
    
    def get_default_config(self) -> AIConfig:
        """Get default configuration for OpenAI"""
        return AIConfig(
            model="gpt-4o-mini",
            max_tokens=2000,
            temperature=0.1,
            timeout=30,
            stream=False,
            system_prompt=BLENDER_SYSTEM_PROMPT
        )
