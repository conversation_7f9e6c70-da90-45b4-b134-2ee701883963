"""
Custom OpenAI-Compatible Provider
=================================

Custom AI provider for OpenAI-compatible APIs like Ollama, LocalAI, vLLM, etc.
Allows users to specify custom endpoints and model IDs.
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Optional, AsyncGenerator, Dict, Any
import re

# Ensure lib directory is in Python path for OpenAI dependencies
current_dir = Path(__file__).parent.parent.parent
lib_path = current_dir / "lib"
if str(lib_path) not in sys.path:
    sys.path.insert(0, str(lib_path))

try:
    from openai import AsyncOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    AsyncOpenAI = None

from .base import (
    AIProvider, AIMessage, AIResponse, AIConfig, AIError, 
    AIProviderError, AIRateLimitError, AITimeoutError,
    BLENDER_SYSTEM_PROMPT
)
from ..utils.logger import get_logger


class CustomProvider(AIProvider):
    """Custom OpenAI-compatible API provider"""
    
    def __init__(self, api_key: str, config: Optional[AIConfig] = None, 
                 base_url: str = "http://localhost:11434/v1", 
                 provider_name: str = "custom"):
        if not OPENAI_AVAILABLE:
            raise AIProviderError("OpenAI library not available. Install with: pip install openai>=1.50.0")
        
        super().__init__(api_key, config)
        self.base_url = base_url
        self.provider_name = provider_name
        self.logger = get_logger(f"{__name__}.CustomProvider")
        self._client = None
        self._available_models = []
        
    @property
    def name(self) -> str:
        return self.provider_name
    
    @property
    def supported_models(self) -> List[str]:
        """Return cached models or default list"""
        if self._available_models:
            return self._available_models
        
        # Default models for common providers
        default_models = [
            "llama3.2:latest",
            "llama3.1:latest", 
            "codellama:latest",
            "mistral:latest",
            "phi3:latest",
            "qwen2.5:latest",
            "custom-model"  # Placeholder for user-defined models
        ]
        return default_models
    
    def _get_client(self) -> AsyncOpenAI:
        """Get or create OpenAI-compatible client"""
        if self._client is None:
            self._client = AsyncOpenAI(
                api_key=self.api_key or "not-needed",  # Some local APIs don't need keys
                base_url=self.base_url
            )
        return self._client
    
    def validate_api_key(self) -> bool:
        """Validate the API connection"""
        try:
            # For custom providers, we'll try to list models to validate connection
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                models = loop.run_until_complete(self._fetch_available_models())
                return len(models) > 0
            finally:
                loop.close()
                
        except Exception as e:
            self.logger.error(f"Custom provider validation failed: {e}")
            return False
    
    async def _fetch_available_models(self) -> List[str]:
        """Fetch available models from the API"""
        try:
            client = self._get_client()
            models_response = await client.models.list()
            
            model_ids = []
            for model in models_response.data:
                model_ids.append(model.id)
            
            self._available_models = model_ids
            self.logger.info(f"Fetched {len(model_ids)} models from {self.base_url}")
            return model_ids
            
        except Exception as e:
            self.logger.warning(f"Could not fetch models from {self.base_url}: {e}")
            return []
    
    def _convert_messages(self, messages: List[AIMessage]) -> List[Dict[str, str]]:
        """Convert AIMessage objects to OpenAI format"""
        openai_messages = []
        
        # Add system prompt if not present
        has_system = any(msg.role == "system" for msg in messages)
        if not has_system:
            openai_messages.append({
                "role": "system",
                "content": BLENDER_SYSTEM_PROMPT
            })
        
        # Convert messages
        for msg in messages:
            openai_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        return openai_messages
    
    def _extract_code(self, content: str) -> str:
        """Extract Python code from response"""
        # Remove markdown code blocks
        code_pattern = r'```(?:python)?\s*(.*?)\s*```'
        matches = re.findall(code_pattern, content, re.DOTALL)
        
        if matches:
            return matches[0].strip()
        
        # If no code blocks found, return the content as-is
        return content.strip()
    
    async def generate_response(
        self, 
        messages: List[AIMessage], 
        config: Optional[AIConfig] = None
    ) -> AIResponse:
        """Generate a response using custom OpenAI-compatible API"""
        
        if not messages:
            raise AIProviderError("No messages provided")
        
        # Use provided config or default
        cfg = config or self.config
        client = self._get_client()
        
        try:
            self.logger.debug(f"Generating response with model: {cfg.model} at {self.base_url}")
            
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Make API call
            response = await client.chat.completions.create(
                model=cfg.model,
                messages=openai_messages,
                max_tokens=cfg.max_tokens,
                temperature=cfg.temperature,
                timeout=cfg.timeout,
                stream=False
            )
            
            # Extract response content
            content = response.choices[0].message.content
            if not content:
                raise AIProviderError(f"Empty response from {self.provider_name}")
            
            # Extract code from response
            code_content = self._extract_code(content)
            
            # Create response object
            ai_response = AIResponse(
                content=code_content,
                model=cfg.model,
                provider=self.name,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                    "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                    "total_tokens": response.usage.total_tokens if response.usage else 0,
                },
                finish_reason=response.choices[0].finish_reason
            )
            
            self.logger.debug(f"Response generated successfully from {self.provider_name}")
            return ai_response
            
        except asyncio.TimeoutError:
            raise AITimeoutError(f"{self.provider_name} request timed out after {cfg.timeout} seconds")
        except Exception as e:
            error_msg = str(e).lower()
            if "rate limit" in error_msg:
                raise AIRateLimitError(f"{self.provider_name} rate limit exceeded: {e}")
            elif "unauthorized" in error_msg or "forbidden" in error_msg:
                raise AIProviderError(f"Authentication failed for {self.provider_name}: {e}")
            elif "not found" in error_msg:
                raise AIProviderError(f"Model {cfg.model} not found on {self.provider_name}: {e}")
            else:
                raise AIProviderError(f"{self.provider_name} API error: {e}")
    
    async def generate_stream(
        self, 
        messages: List[AIMessage], 
        config: Optional[AIConfig] = None
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response using custom OpenAI-compatible API"""
        
        if not messages:
            raise AIProviderError("No messages provided")
        
        # Use provided config or default
        cfg = config or self.config
        client = self._get_client()
        
        try:
            self.logger.debug(f"Generating streaming response with model: {cfg.model} at {self.base_url}")
            
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Make streaming API call
            stream = await client.chat.completions.create(
                model=cfg.model,
                messages=openai_messages,
                max_tokens=cfg.max_tokens,
                temperature=cfg.temperature,
                timeout=cfg.timeout,
                stream=True
            )
            
            # Yield chunks as they arrive
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except asyncio.TimeoutError:
            raise AITimeoutError(f"{self.provider_name} streaming request timed out after {cfg.timeout} seconds")
        except Exception as e:
            error_msg = str(e).lower()
            if "rate limit" in error_msg:
                raise AIRateLimitError(f"{self.provider_name} rate limit exceeded: {e}")
            elif "unauthorized" in error_msg or "forbidden" in error_msg:
                raise AIProviderError(f"Authentication failed for {self.provider_name}: {e}")
            else:
                raise AIProviderError(f"{self.provider_name} streaming error: {e}")
    
    def get_default_config(self) -> AIConfig:
        """Get default configuration for custom provider"""
        return AIConfig(
            model="llama3.2:latest",  # Default model
            max_tokens=2000,
            temperature=0.1,
            timeout=30,
            stream=False,
            system_prompt=BLENDER_SYSTEM_PROMPT
        )
    
    def update_base_url(self, new_url: str):
        """Update the base URL and reset client"""
        self.base_url = new_url
        self._client = None
        self._available_models = []  # Clear cached models
        self.logger.info(f"Updated base URL to: {new_url}")
    
    def add_custom_model(self, model_id: str):
        """Add a custom model ID to supported models"""
        if model_id not in self._available_models:
            self._available_models.append(model_id)
            self.logger.info(f"Added custom model: {model_id}")
    
    def refresh_models(self):
        """Refresh available models from API"""
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                models = loop.run_until_complete(self._fetch_available_models())
                self.logger.info(f"Refreshed models: {len(models)} found")
                return models
            finally:
                loop.close()
                
        except Exception as e:
            self.logger.error(f"Failed to refresh models: {e}")
            return []


# Predefined custom provider configurations
CUSTOM_PROVIDER_PRESETS = {
    "ollama": {
        "name": "Ollama",
        "base_url": "http://localhost:11434/v1",
        "default_model": "llama3.2:latest",
        "description": "Local Ollama instance"
    },
    "localai": {
        "name": "LocalAI", 
        "base_url": "http://localhost:8080/v1",
        "default_model": "gpt-3.5-turbo",
        "description": "Local LocalAI instance"
    },
    "vllm": {
        "name": "vLLM",
        "base_url": "http://localhost:8000/v1", 
        "default_model": "meta-llama/Llama-2-7b-chat-hf",
        "description": "vLLM inference server"
    },
    "text-generation-webui": {
        "name": "Text Generation WebUI",
        "base_url": "http://localhost:5000/v1",
        "default_model": "gpt-3.5-turbo",
        "description": "Text Generation WebUI with OpenAI API"
    },
    "lm-studio": {
        "name": "LM Studio",
        "base_url": "http://localhost:1234/v1",
        "default_model": "local-model",
        "description": "LM Studio local server"
    },
    "custom": {
        "name": "Custom",
        "base_url": "http://localhost:8080/v1",
        "default_model": "custom-model",
        "description": "Custom OpenAI-compatible endpoint"
    }
}
