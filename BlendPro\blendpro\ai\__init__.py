"""
BlendPro AI Package
==================

AI model integrations for BlendPro supporting multiple providers:
- OpenAI (GPT-4o, GPT-4o-mini, GPT-4-turbo, GPT-3.5-turbo)
- Anthropic (Claude-3.5-<PERSON><PERSON>, <PERSON>-3-<PERSON><PERSON>)
- Google (Gemini-1.5-Pro, Gemini-1.5-Flash)
"""

import sys
from pathlib import Path

# Ensure lib directory is in Python path for AI dependencies
current_dir = Path(__file__).parent.parent.parent
lib_path = current_dir / "lib"
if str(lib_path) not in sys.path:
    sys.path.insert(0, str(lib_path))

from .base import AIProvider, AIResponse, AIError
from .openai_provider import OpenAIProvider
from .anthropic_provider import AnthropicProvider
from .google_provider import GoogleProvider
from .custom_provider import CustomProvider, CUSTOM_PROVIDER_PRESETS
from .manager import AIManager

__all__ = [
    "AIProvider",
    "AIResponse",
    "AIError",
    "OpenAIProvider",
    "AnthropicProvider",
    "GoogleProvider",
    "CustomProvider",
    "CUSTOM_PROVIDER_PRESETS",
    "AIManager",
]
