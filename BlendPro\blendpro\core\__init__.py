"""
BlendPro Core Package
====================

Core functionality for BlendPro including addon management,
properties, and configuration.
"""

import sys
from pathlib import Path

# Ensure lib directory is in Python path for core dependencies
current_dir = Path(__file__).parent.parent.parent
lib_path = current_dir / "lib"
if str(lib_path) not in sys.path:
    sys.path.insert(0, str(lib_path))

from .addon_manager import AddonManager
from .properties import init_properties, clear_properties

__all__ = [
    "AddonManager",
    "init_properties",
    "clear_properties",
]
