"""
BlendPro UI Package
==================

Modern user interface components for BlendPro using Blender 4.2+ APIs.
"""

import sys
from pathlib import Path

# Ensure lib directory is in Python path for UI dependencies
current_dir = Path(__file__).parent.parent.parent
lib_path = current_dir / "lib"
if str(lib_path) not in sys.path:
    sys.path.insert(0, str(lib_path))

from .panels import BlendProPanel
from .operators import (
    BlendProExecuteOperator,
    BlendProClearChatOperator,
    BlendProShowCodeOperator,
    BlendProDeleteMessageOperator,
    BlendProSettingsOperator
)
from .preferences import BlendProPreferences

__all__ = [
    "BlendProPanel",
    "BlendProExecuteOperator",
    "BlendProClearChatOperator", 
    "BlendProShowCodeOperator",
    "BlendProDeleteMessageOperator",
    "BlendProSettingsOperator",
    "BlendProPreferences",
]
